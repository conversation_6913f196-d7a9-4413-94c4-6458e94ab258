<?php

namespace Tests\Unit;

use App\Services\UnaApiService;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class UnaApiServiceTest extends TestCase
{
    protected UnaApiService $unaApiService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->unaApiService = new UnaApiService();
    }

    public function test_get_requests_with_various_endpoints()
    {
        // Set up configuration
        config([
            'una.api_domain' => 'https://api.una.com',
            'una.api_token' => 'test-token',
        ]);

        // Mock successful response
        Http::fake([
            'https://api.una.com/users' => Http::response([
                'data' => ['user1', 'user2'],
                'status' => 'success'
            ], 200),
        ]);

        $result = $this->unaApiService->get('users');

        $this->assertEquals([
            'data' => ['user1', 'user2'],
            'status' => 'success'
        ], $result);

        // Verify the request was made correctly
        Http::assertSent(function ($request) {
            return $request->url() === 'https://api.una.com/users' &&
                   $request->hasHeader('Authorization', 'Bearer test-token');
        });
    }

    public function test_get_requests_with_leading_and_trailing_slashes()
    {
        config([
            'una.api_domain' => 'https://api.una.com/',
            'una.api_token' => 'test-token',
        ]);

        Http::fake([
            'https://api.una.com/posts/123' => Http::response(['id' => 123], 200),
        ]);

        // Test with leading slash
        $result = $this->unaApiService->get('/posts/123');

        $this->assertEquals(['id' => 123], $result);

        Http::assertSent(function ($request) {
            return $request->url() === 'https://api.una.com/posts/123';
        });
    }

    public function test_get_requests_with_root_endpoint()
    {
        config([
            'una.api_domain' => 'https://api.una.com',
            'una.api_token' => 'test-token',
        ]);

        Http::fake([
            'https://api.una.com/' => Http::response(['status' => 'ok'], 200),
        ]);

        $result = $this->unaApiService->get('/');

        $this->assertEquals(['status' => 'ok'], $result);
    }

    public function test_get_requests_with_authentication()
    {
        config([
            'una.api_domain' => 'https://api.una.com',
            'una.api_token' => 'secret-api-token-123',
        ]);

        Http::fake([
            'https://api.una.com/protected' => Http::response(['protected' => 'data'], 200),
        ]);

        $result = $this->unaApiService->get('protected');

        $this->assertEquals(['protected' => 'data'], $result);

        Http::assertSent(function ($request) {
            return $request->hasHeader('Authorization', 'Bearer secret-api-token-123');
        });
    }

    public function test_get_requests_with_network_failures()
    {
        config([
            'una.api_domain' => 'https://api.una.com',
            'una.api_token' => 'test-token',
        ]);

        // Mock network failure
        Http::fake([
            'https://api.una.com/failing-endpoint' => Http::response(null, 500),
        ]);

        $result = $this->unaApiService->get('failing-endpoint');

        $this->assertNull($result);
    }

    public function test_post_requests_with_data()
    {
        config([
            'una.api_url' => 'https://api.una.com',
            'una.api_token' => 'test-token',
        ]);

        $postData = [
            'title' => 'Test Post',
            'content' => 'This is test content',
            'author' => 'Test Author'
        ];

        Http::fake([
            'https://api.una.com/posts' => Http::response([
                'id' => 456,
                'title' => 'Test Post',
                'status' => 'created'
            ], 201),
        ]);

        $result = $this->unaApiService->post('posts', $postData);

        $this->assertEquals([
            'id' => 456,
            'title' => 'Test Post',
            'status' => 'created'
        ], $result);

        Http::assertSent(function ($request) use ($postData) {
            return $request->url() === 'https://api.una.com/posts' &&
                   $request->hasHeader('Authorization', 'Bearer test-token') &&
                   $request->data() === $postData;
        });
    }

    public function test_post_requests_with_authentication()
    {
        config([
            'una.api_url' => 'https://api.una.com',
            'una.api_token' => 'secure-token-456',
        ]);

        Http::fake([
            'https://api.una.com/secure-endpoint' => Http::response(['success' => true], 200),
        ]);

        $result = $this->unaApiService->post('secure-endpoint', ['data' => 'test']);

        $this->assertEquals(['success' => true], $result);

        Http::assertSent(function ($request) {
            return $request->hasHeader('Authorization', 'Bearer secure-token-456');
        });
    }

    public function test_post_requests_with_network_failures()
    {
        config([
            'una.api_url' => 'https://api.una.com',
            'una.api_token' => 'test-token',
        ]);

        Http::fake([
            'https://api.una.com/failing-post' => Http::response(null, 503),
        ]);

        $result = $this->unaApiService->post('failing-post', ['test' => 'data']);

        $this->assertNull($result);
    }

    public function test_url_building_with_different_configurations()
    {
        // Test with trailing slash in domain
        config([
            'una.api_domain' => 'https://api.example.com/',
            'una.api_token' => 'test-token',
        ]);

        Http::fake([
            'https://api.example.com/test' => Http::response(['test' => 'success'], 200),
        ]);

        $result = $this->unaApiService->get('test');
        $this->assertEquals(['test' => 'success'], $result);

        // Test with no trailing slash in domain
        config([
            'una.api_domain' => 'https://api.example.com',
        ]);

        Http::fake([
            'https://api.example.com/test2' => Http::response(['test2' => 'success'], 200),
        ]);

        $result = $this->unaApiService->get('/test2');
        $this->assertEquals(['test2' => 'success'], $result);
    }

    public function test_token_handling_edge_cases()
    {
        // Test with null token
        config([
            'una.api_domain' => 'https://api.una.com',
            'una.api_token' => null,
        ]);

        Http::fake([
            '*' => Http::response(['public' => 'data'], 200),
        ]);

        $result = $this->unaApiService->get('no-auth');

        $this->assertEquals(['public' => 'data'], $result);

        // Just verify the request was made to the correct URL
        Http::assertSent(function ($request) {
            return $request->url() === 'https://api.una.com/no-auth';
        });

        // Test with empty token
        config([
            'una.api_token' => '',
        ]);

        $result = $this->unaApiService->get('empty-auth');

        $this->assertEquals(['public' => 'data'], $result);

        // Verify the second request was made
        Http::assertSent(function ($request) {
            return $request->url() === 'https://api.una.com/empty-auth';
        });
    }

    protected function tearDown(): void
    {
        Http::fake(); // Reset HTTP fake
        parent::tearDown();
    }
}
