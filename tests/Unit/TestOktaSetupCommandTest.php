<?php

namespace Tests\Unit;

use App\Console\Commands\TestOktaSetup;
use App\Services\OktaService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;
use Mockery;
use Tests\TestCase;

class TestOktaSetupCommandTest extends TestCase
{
    protected $oktaService;

    protected function setUp(): void
    {
        parent::setUp();

        // Set up basic Okta configuration
        config([
            'services.okta.domain' => 'test-domain.okta.com',
            'services.okta.client_id' => 'test_client_id',
            'services.okta.client_secret' => 'test_client_secret',
            'services.okta.redirect_uri' => 'http://localhost/sso-auth/callback',
            'services.okta.issuer' => 'https://test-domain.okta.com/oauth2/default',
        ]);
    }

    public function test_command_with_valid_configuration()
    {
        // Mock the OktaService
        $oktaService = Mockery::mock(OktaService::class);
        $this->app->instance(OktaService::class, $oktaService);

        // Mock successful responses for the methods that are actually called
        $oktaService->shouldReceive('generatePkceChallenge')
            ->andReturn([
                'code_verifier' => 'test-verifier',
                'code_challenge' => 'test-challenge'
            ]);

        $oktaService->shouldReceive('buildAuthorizationUrl')
            ->andReturn('https://test-domain.okta.com/oauth2/default/v1/authorize?client_id=test');

        // Run the command
        $exitCode = Artisan::call('okta:test-setup');

        $this->assertEquals(0, $exitCode);
        $output = Artisan::output();
        $this->assertStringContainsString('Testing Okta Configuration', $output);
        $this->assertStringContainsString('Testing Configuration', $output);
        $this->assertStringContainsString('Okta setup test completed', $output);
    }

    public function test_command_with_invalid_configuration()
    {
        // Clear configuration to simulate missing config
        config([
            'services.okta.domain' => '',
            'services.okta.client_id' => '',
            'services.okta.client_secret' => '',
        ]);

        $exitCode = Artisan::call('okta:test-setup');

        $this->assertEquals(0, $exitCode); // Command doesn't return error codes
        $output = Artisan::output();
        $this->assertStringContainsString('OKTA_DOMAIN is not set', $output);
    }

    public function test_command_with_full_flag()
    {
        // Mock the OktaService for full test
        $oktaService = Mockery::mock(OktaService::class);
        $this->app->instance(OktaService::class, $oktaService);

        // Mock the methods that are actually called
        $oktaService->shouldReceive('generatePkceChallenge')
            ->andReturn([
                'code_verifier' => 'test-verifier',
                'code_challenge' => 'test-challenge'
            ]);

        $oktaService->shouldReceive('buildAuthorizationUrl')
            ->andReturn('https://test-domain.okta.com/oauth2/default/v1/authorize?client_id=test');

        $exitCode = Artisan::call('okta:test-setup', ['--full' => true]);

        $this->assertEquals(0, $exitCode);
        $output = Artisan::output();
        $this->assertStringContainsString('Testing Okta Configuration', $output);
        $this->assertStringContainsString('Okta setup test completed', $output);
    }

    public function test_command_with_network_failures()
    {
        // Mock the OktaService to throw exceptions
        $oktaService = Mockery::mock(OktaService::class);
        $this->app->instance(OktaService::class, $oktaService);

        // Mock PKCE generation to fail
        $oktaService->shouldReceive('generatePkceChallenge')
            ->andThrow(new \Exception('Network error: Connection timeout'));

        $oktaService->shouldReceive('buildAuthorizationUrl')
            ->andReturn('https://test-domain.okta.com/oauth2/default/v1/authorize?client_id=test');

        $exitCode = Artisan::call('okta:test-setup');

        $this->assertEquals(0, $exitCode); // Command doesn't return error codes
        $output = Artisan::output();
        $this->assertStringContainsString('Testing Okta Configuration', $output);
        $this->assertStringContainsString('Network error: Connection timeout', $output);
    }

    public function test_individual_test_methods()
    {
        // Mock OktaService for the command
        $oktaService = Mockery::mock(OktaService::class);
        $command = new TestOktaSetup($oktaService);

        // The individual test methods are private, so we can't test them directly
        // Instead, we'll test that the command can be instantiated properly
        $this->assertInstanceOf(TestOktaSetup::class, $command);

        // Test that the command has the correct name
        $this->assertEquals('okta:test-setup', $command->getName());
    }

    public function test_command_handles_partial_failures_gracefully()
    {
        // Mock the OktaService with mixed success/failure
        $oktaService = Mockery::mock(OktaService::class);
        $this->app->instance(OktaService::class, $oktaService);

        // PKCE generation succeeds
        $oktaService->shouldReceive('generatePkceChallenge')
            ->andReturn([
                'code_verifier' => 'test-verifier',
                'code_challenge' => 'test-challenge'
            ]);

        // Authorization URL building fails
        $oktaService->shouldReceive('buildAuthorizationUrl')
            ->andThrow(new \Exception('Authorization URL building failed'));

        $exitCode = Artisan::call('okta:test-setup');

        $this->assertEquals(0, $exitCode); // Command doesn't return error codes
        $output = Artisan::output();
        $this->assertStringContainsString('Testing Okta Configuration', $output);
        $this->assertStringContainsString('Authorization URL building failed', $output);
    }

    public function test_command_with_missing_environment_variables()
    {
        // Test with completely missing Okta configuration
        config([
            'services.okta.domain' => '',
            'services.okta.client_id' => '',
            'services.okta.client_secret' => '',
            'services.okta.redirect_uri' => '',
        ]);

        $exitCode = Artisan::call('okta:test-setup');

        $this->assertEquals(0, $exitCode); // Command doesn't return error codes
        $output = Artisan::output();
        $this->assertStringContainsString('OKTA_DOMAIN is not set', $output);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
