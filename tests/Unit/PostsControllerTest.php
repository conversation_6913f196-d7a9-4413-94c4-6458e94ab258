<?php

namespace Tests\Unit;

use App\Http\Controllers\Api\PostsController;
use App\Models\User;
use App\Services\UnaApiService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Mockery;
use Tests\TestCase;
use Tests\RefreshInMemoryDatabase;

class PostsControllerTest extends TestCase
{
    use RefreshInMemoryDatabase;

    protected PostsController $controller;

    protected function setUp(): void
    {
        parent::setUp();

        $this->refreshInMemoryDatabase();

        $this->controller = new PostsController();
    }

    public function test_index_method_with_successful_una_api_service_response()
    {
        // Mock successful response from UnaApiService
        $expectedResponse = [
            'userId' => 1,
            'id' => 1,
            'title' => 'delectus aut autem',
            'completed' => false
        ];

        // Mock the UnaApiService
        $unaApiService = Mockery::mock(UnaApiService::class);
        $unaApiService->shouldReceive('get')
            ->once()
            ->with('todos/1')
            ->andReturn($expectedResponse);

        $response = $this->controller->index($unaApiService);

        $this->assertEquals(200, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals(['posts' => $expectedResponse], $responseData);
    }

    public function test_index_method_with_una_api_service_failures()
    {
        // Mock UnaApiService throwing an exception
        $unaApiService = Mockery::mock(UnaApiService::class);
        $unaApiService->shouldReceive('get')
            ->once()
            ->with('todos/1')
            ->andThrow(new \Exception('UNA API service unavailable'));

        // The actual controller doesn't handle exceptions, so this will throw
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('UNA API service unavailable');

        $this->controller->index($unaApiService);
    }

    public function test_index_method_returns_consistent_structure()
    {
        // Test that the index method always returns the same structure
        $expectedResponse = [
            'userId' => 1,
            'id' => 1,
            'title' => 'test todo',
            'completed' => true
        ];

        $unaApiService = Mockery::mock(UnaApiService::class);
        $unaApiService->shouldReceive('get')
            ->once()
            ->with('todos/1')
            ->andReturn($expectedResponse);

        $response = $this->controller->index($unaApiService);

        $this->assertEquals(200, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('posts', $responseData);
        $this->assertEquals($expectedResponse, $responseData['posts']);
    }

    public function test_store_method_returns_success_message()
    {
        $postData = [
            'title' => 'New Test Post',
            'content' => 'This is new test content',
            'category' => 'technology',
        ];

        $request = Request::create('/api/posts', 'POST', $postData);
        $response = $this->controller->store($request);

        $this->assertEquals(200, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals(['message' => 'Post created'], $responseData);
    }

    public function test_store_method_with_empty_request_data()
    {
        $request = Request::create('/api/posts', 'POST', []);
        $response = $this->controller->store($request);

        $this->assertEquals(200, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals(['message' => 'Post created'], $responseData);
    }

    public function test_store_method_with_various_request_data()
    {
        $testCases = [
            ['title' => 'Test Post'],
            ['content' => 'Test content'],
            ['title' => 'Test', 'content' => 'Content', 'extra' => 'data'],
            null, // No data
        ];

        foreach ($testCases as $data) {
            $request = Request::create('/api/posts', 'POST', $data ?? []);
            $response = $this->controller->store($request);

            $this->assertEquals(200, $response->getStatusCode());
            $responseData = json_decode($response->getContent(), true);
            $this->assertEquals(['message' => 'Post created'], $responseData);
        }
    }

    public function test_index_method_handles_empty_responses_gracefully()
    {
        // Test when UNA API returns empty array
        $unaApiService = Mockery::mock(UnaApiService::class);
        $unaApiService->shouldReceive('get')
            ->once()
            ->with('todos/1')
            ->andReturn([]);

        $response = $this->controller->index($unaApiService);

        $this->assertEquals(200, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals(['posts' => []], $responseData);
    }

    public function test_index_method_handles_null_responses()
    {
        // Test when UNA API returns null
        $unaApiService = Mockery::mock(UnaApiService::class);
        $unaApiService->shouldReceive('get')
            ->once()
            ->with('todos/1')
            ->andReturn(null);

        $response = $this->controller->index($unaApiService);

        $this->assertEquals(200, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals(['posts' => null], $responseData);
    }

    public function test_controller_instantiation()
    {
        // Test that the controller can be instantiated without issues
        $controller = new PostsController();
        $this->assertInstanceOf(PostsController::class, $controller);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
