APP_NAME=Laravel
APP_ENV=testing
APP_KEY=base64:AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
BCRYPT_ROUNDS=4

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
DB_DATABASE=:memory:

SESSION_DRIVER=array
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync

CACHE_STORE=array

MAIL_MAILER=array
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Okta SSO Configuration for testing
OKTA_DOMAIN=test-domain.okta.com
OKTA_AUTH_SERVER_ID=default
OKTA_CLIENT_ID=test_client_id
OKTA_CLIENT_SECRET=test_client_secret
OKTA_REDIRECT_URI=http://localhost:8000/sso-auth/callback
OKTA_ISSUER=https://test-domain.okta.com/oauth2/default

# Mobile App Configuration for testing
APP_SCHEME=testapp
APP_AUTH_CALLBACK_PATH=auth-callback

# Frontend Configuration for testing
FRONTEND_URL=http://localhost:3000

# Token Signing Key for testing
TOKEN_SIGNING_KEY=base64:hFUgqM1sr1VcMI9UuGtaNHwXPTBT1TUTpiCbN9cxGUM=

# Integration Testing disabled
OKTA_INTEGRATION_TEST=false

# UNA API Configuration for testing
UNA_API_TOKEN=test_token
UNA_API_DOMAIN=https://test.example.com
UNA_SESSION_TTL=1
