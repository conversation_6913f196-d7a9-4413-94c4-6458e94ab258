<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PageController;
use App\Http\Controllers\Api\AuthController;

Route::get('/', [PageController::class, 'home']);

// App-to-Middleware Authentication Routes
Route::prefix('auth')->group(function () {
    Route::get('/login', [AuthController::class, 'login'])->name('auth.login');
    Route::get('/success', [AuthController::class, 'success'])->name('auth.success');
    Route::get('/error', [AuthController::class, 'error'])->name('auth.error');
    Route::post('/exchange-token', [AuthController::class, 'exchangeToken'])
        ->withoutMiddleware([\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken::class])
        ->name('auth.exchange-token');
    Route::post('/refresh', [AuthController::class, 'refresh'])->name('auth.refresh');
});

// Middleware-to-SSO Provider Routes (Okta callbacks)
Route::prefix('sso-auth')->group(function () {
    Route::get('/callback', [AuthController::class, 'callback'])->name('sso-auth.callback');
});
